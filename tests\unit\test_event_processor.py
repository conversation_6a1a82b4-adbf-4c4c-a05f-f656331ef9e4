"""
Unit tests for event processor service.

This module tests the event processing functionality including field mapping,
duplicate handling, and database operations.
"""

import json
import pytest
from unittest.mock import Mock, patch

from src.agent_event_processor.models.events import AgentEvent, EventType
from src.agent_event_processor.services.event_processor import EventProcessor
from src.agent_event_processor.config.settings import Settings


class TestEventProcessor:
    """Test suite for EventProcessor."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        settings = Mock(spec=Settings)
        settings.database = Mock()
        settings.enable_business_metrics = True
        settings.enable_custom_metrics = True
        return settings

    @pytest.fixture
    def event_processor(self, mock_settings):
        """Create event processor instance for testing."""
        with patch(
            "src.agent_event_processor.services.event_processor.DatabaseService"
        ), patch(
            "src.agent_event_processor.services.event_processor.DimensionTableManager"
        ), patch(
            "src.agent_event_processor.services.event_processor.FactTableManager"
        ):
            return EventProcessor(mock_settings)

    def test_transform_login_event_to_fact_data(self, event_processor):
        """Test transformation of login event to fact data."""
        # Create test event
        event_data = {
            "timestamp": "2025-02-05T11:44:18.031Z",
            "eventType": "Login",
            "agencyOrElement": "Brandon911",
            "agent": "dycj",
            "event_data": {
                "login": {
                    "mediaLabel": "_ML_194D5ECDE50C0001C46A@BrandonMB",
                    "uri": "tel:+2045553006",
                    "agentRole": "Rural - CT",
                    "workstation": "OP6",
                    "deviceName": "Headset",
                    "reason": "normal",
                }
            },
        }

        event = AgentEvent.model_validate(event_data)

        # Mock dimension keys
        dimension_keys = Mock()
        dimension_keys.tenant_key = 1
        dimension_keys.agent_key = 2
        dimension_keys.date_key = 20250205
        dimension_keys.time_key = 114400
        dimension_keys.queue_key = None

        # Test transformation
        fact_data = event_processor._transform_to_fact_data(event, dimension_keys)

        assert fact_data["tenant_key"] == 1
        assert fact_data["agent_key"] == 2
        assert fact_data["event_type"] == EventType.LOGIN
        assert fact_data["media_label"] == "_ML_194D5ECDE50C0001C46A@BrandonMB"
        assert fact_data["workstation"] == "OP6"
        assert fact_data["device_name"] == "Headset"
        assert fact_data["reason_code"] == "normal"

    def test_transform_agent_busied_out_event_to_fact_data(self, event_processor):
        """Test transformation of agent busied out event with reason, action, and duration fields."""
        event_data = {
            "timestamp": "2025-02-05T14:34:08.951Z",
            "eventType": "AgentBusiedOut",
            "agencyOrElement": "Brandon911",
            "agent": "keem",
            "event_data": {
                "agentbusiedout": {
                    "mediaLabel": "_ML_194D5F04D38C0001C46D@BrandonMB",
                    "uri": "tel:+2045553007",
                    "agentRole": "BPS - CT_Dispatch",
                    "workstation": "OP7",
                    "reason": "break",
                    "action": "manual",
                    "duration": "900",
                }
            },
        }

        event = AgentEvent.model_validate(event_data)

        # Mock dimension keys
        dimension_keys = Mock()
        dimension_keys.tenant_key = 1
        dimension_keys.agent_key = 2
        dimension_keys.date_key = 20250205
        dimension_keys.time_key = 143400
        dimension_keys.queue_key = None

        # Test transformation
        fact_data = event_processor._transform_to_fact_data(event, dimension_keys)

        assert fact_data["event_type"] == EventType.AGENT_BUSIED_OUT
        assert fact_data["reason_code"] == "break"
        assert fact_data["busied_out_action"] == "manual"
        assert fact_data["busied_out_duration"] == "900"
        assert fact_data["workstation"] == "OP7"

    def test_transform_logout_event_with_voice_qos(self, event_processor):
        """Test transformation of logout event with voice QoS data."""
        event_data = {
            "timestamp": "2025-02-05T11:46:03.421Z",
            "eventType": "Logout",
            "agencyOrElement": "Brandon911",
            "agent": "lacd",
            "event_data": {
                "logout": {
                    "mediaLabel": "_ML_194D357A8C8C0001C329@BrandonMB",
                    "responseCode": "16",
                    "voiceQOS": {
                        "mediaIpSourceAddr": "*************",
                        "mediaIpDestAddr": "**************",
                        "mediaUdpRtpSourcePort": "21464",
                        "mediaUdpRtpDestPort": "4000",
                        "mediaNumOfRtpPktLost": "0",
                        "mediaRtpJitter": "6",
                        "mediaRtpLatency": "1",
                    },
                }
            },
        }

        event = AgentEvent.model_validate(event_data)

        # Mock dimension keys
        dimension_keys = Mock()
        dimension_keys.tenant_key = 1
        dimension_keys.agent_key = 2
        dimension_keys.date_key = 20250205
        dimension_keys.time_key = 114600
        dimension_keys.queue_key = None

        # Test transformation
        fact_data = event_processor._transform_to_fact_data(event, dimension_keys)

        assert fact_data["event_type"] == EventType.LOGOUT
        assert fact_data["media_label"] == "_ML_194D357A8C8C0001C329@BrandonMB"
        # Voice QoS data should be preserved in event_data_json
        assert fact_data["event_data_json"] is not None
        # Check that voice QoS data is in the JSON
        if isinstance(fact_data["event_data_json"], str):
            event_json = json.loads(fact_data["event_data_json"])
        else:
            event_json = fact_data["event_data_json"]
        assert "logout" in event_json
        assert "voiceQOS" in event_json["logout"]
        assert event_json["logout"]["voiceQOS"]["mediaIpSourceAddr"] == "*************"

    def test_transform_queue_state_change_event(self, event_processor):
        """Test transformation of queue state change event."""
        event_data = {
            "timestamp": "2025-08-22T12:58:03.01Z",  # Use current date
            "eventType": "QueueStateChange",
            "agencyOrElement": "esrp.state.il.us",
            "agent": ".",
            "event_data": {
                "QueueStateChange": {
                    "queueId": "2470001234",
                    "queueName": "2470001234",
                    "direction": "",
                    "count": "1",
                }
            },
        }

        event = AgentEvent.model_validate(event_data)

        # Mock dimension keys
        dimension_keys = Mock()
        dimension_keys.tenant_key = 1
        dimension_keys.agent_key = 2
        dimension_keys.date_key = 20250822
        dimension_keys.time_key = 125800
        dimension_keys.queue_key = None

        # Test transformation
        fact_data = event_processor._transform_to_fact_data(event, dimension_keys)

        assert fact_data["event_type"] == EventType.QUEUE_STATE_CHANGE
        # Queue data should be preserved in event_data_json, not as separate fields
        assert fact_data["event_data_json"] is not None
        # Check that queue data is in the JSON
        if isinstance(fact_data["event_data_json"], str):
            event_json = json.loads(fact_data["event_data_json"])
        else:
            event_json = fact_data["event_data_json"]
        assert "QueueStateChange" in event_json
        assert event_json["QueueStateChange"]["queueId"] == "2470001234"
        assert event_json["QueueStateChange"]["queueName"] == "2470001234"

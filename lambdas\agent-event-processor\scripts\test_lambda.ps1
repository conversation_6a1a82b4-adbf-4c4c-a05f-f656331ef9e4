# PowerShell script to test Agent Event Processor Lambda
# Usage: .\test_lambda.ps1 [event_file_path]
# If no event file is provided, it will test all events in test-events directory

param(
    [string]$EventFile = ""
)

$ErrorActionPreference = "Stop"

Write-Host "Testing Agent Event Processor Lambda..." -ForegroundColor Green

# Configuration
$LOCALSTACK_ENDPOINT = "http://localhost:4566"
$AWS_REGION = "us-east-1"
$QUEUE_NAME = "agent-events-queue"
$SCRIPT_DIR = Split-Path -Parent $MyInvocation.MyCommand.Definition
$PROJECT_DIR = Split-Path -Parent $SCRIPT_DIR
$TEST_EVENTS_DIR = Join-Path $PROJECT_DIR "test-events"

# Configure AWS CLI for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = $AWS_REGION

# Get queue URL
$QUEUE_URL = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url --queue-name $QUEUE_NAME --region $AWS_REGION --query 'QueueUrl' --output text

Write-Host "Queue URL: $QUEUE_URL"

# Function to send event file to SQS
function Send-EventFile {
    param(
        [string]$EventFilePath
    )

    $eventName = [System.IO.Path]::GetFileNameWithoutExtension($EventFilePath)
    Write-Host "Sending $eventName event to SQS..." -ForegroundColor Yellow

    # Read XML content from file
    $TEST_XML = (Get-Content $EventFilePath -Raw).ToString()

    # Create SQS message body
    $message_body = @{
        Message = $TEST_XML
    } | ConvertTo-Json -Compress

    # Send test message to SQS
    aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs send-message --queue-url $QUEUE_URL --message-body $message_body --region $AWS_REGION

    Write-Host "$eventName event sent!" -ForegroundColor Green
}

# Check if specific event file is provided
if ($EventFile -ne "") {
    if (-not (Test-Path $EventFile)) {
        Write-Host "Error: Event file '$EventFile' not found!" -ForegroundColor Red
        exit 1
    }
    Send-EventFile $EventFile
} else {
    # Test all event files in test-events directory
    if (-not (Test-Path $TEST_EVENTS_DIR)) {
        Write-Host "Error: Test events directory '$TEST_EVENTS_DIR' not found!" -ForegroundColor Red
        exit 1
    }

    Write-Host "Testing all events in $TEST_EVENTS_DIR..." -ForegroundColor Yellow
    $eventFiles = Get-ChildItem -Path $TEST_EVENTS_DIR -Filter "*.xml"

    if ($eventFiles.Count -eq 0) {
        Write-Host "No XML event files found in $TEST_EVENTS_DIR" -ForegroundColor Yellow
        exit 0
    }

    foreach ($eventFile in $eventFiles) {
        if ($eventFile.FullName -and (Test-Path $eventFile.FullName)) {
            Send-EventFile $eventFile.FullName
            Start-Sleep -Seconds 1  # Small delay between events
        }
    }
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green

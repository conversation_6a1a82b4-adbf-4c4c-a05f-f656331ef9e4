"""
Field mapping utilities for agent events.

This module provides extensible field mapping configurations for different
event types, making it easy to add new event types and field mappings.
"""

from typing import Dict, List, Any
from ..models.events import EventType


class FieldMappingConfig:
    """Configuration for field mappings by event type."""

    # Base fields that apply to all events - only essential star schema fields
    BASE_FIELDS = [
        "reason_code:reason",
        "media_label:mediaLabel",
        "workstation:workstation",
        "device_name:deviceName",
    ]

    # Event-specific field mappings - only essential dimensional fields
    EVENT_SPECIFIC_FIELDS = {
        EventType.AGENT_AVAILABLE: [
            "busied_out_action:busiedOutAction",
            "busied_out_duration:duration",
        ],
        EventType.AGENT_BUSIED_OUT: [
            "busied_out_action:action",
            "busied_out_duration:duration",
        ],
    }

    @classmethod
    def get_field_mappings(cls, event_type: EventType) -> List[str]:
        """
        Get field mappings for a specific event type.

        Args:
            event_type: The event type to get mappings for

        Returns:
            List of field mappings in format "db_field:event_field"
        """
        mappings = cls.BASE_FIELDS.copy()

        # Add event-specific mappings
        if event_type in cls.EVENT_SPECIFIC_FIELDS:
            mappings.extend(cls.EVENT_SPECIFIC_FIELDS[event_type])

        return mappings

    @classmethod
    def extract_fields(cls, event, event_type: EventType) -> Dict[str, Any]:
        """
        Extract fields from event based on event type configuration.

        Args:
            event: The agent event object
            event_type: The event type

        Returns:
            Dictionary of extracted field values
        """
        extracted = {}
        mappings = cls.get_field_mappings(event_type)

        for mapping in mappings:
            if ":" not in mapping:
                continue

            db_field, event_field = mapping.split(":", 1)
            value = event.get_value(event_field)

            if value is not None:
                extracted[db_field] = value

        return extracted

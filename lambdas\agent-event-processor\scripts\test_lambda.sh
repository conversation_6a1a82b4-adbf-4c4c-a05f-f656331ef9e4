#!/bin/bash

# Test script for Agent Event Processor Lambda
# This script sends test messages to SQS and monitors Lambda execution
# Usage: ./test_lambda.sh [event_file_path]
# If no event file is provided, it will test all events in test-events directory

set -e

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
AWS_REGION="us-east-1"
QUEUE_NAME="agent-events-queue"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_EVENTS_DIR="$SCRIPT_DIR/../test-events"

echo "Testing Agent Event Processor Lambda..."

# Configure AWS CLI for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=$AWS_REGION

# Get queue URL
QUEUE_URL=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url \
    --queue-name $QUEUE_NAME \
    --region $AWS_REGION \
    --query 'QueueUrl' --output text)

echo "Queue URL: $QUEUE_URL"

# Function to send event file to SQS
send_event_file() {
    local event_file="$1"
    local event_name=$(basename "$event_file" .xml)

    echo "Sending $event_name event to SQS..."

    # Read XML content from file
    TEST_XML=$(cat "$event_file")

    # Send test message to SQS
    aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs send-message \
        --queue-url $QUEUE_URL \
        --message-body "{\"Message\": \"$TEST_XML\"}" \
        --region $AWS_REGION

    echo "$event_name event sent!"
}

# Check if specific event file is provided
if [ $# -eq 1 ]; then
    EVENT_FILE="$1"
    if [ ! -f "$EVENT_FILE" ]; then
        echo "Error: Event file '$EVENT_FILE' not found!"
        exit 1
    fi
    send_event_file "$EVENT_FILE"
else
    # Test all event files in test-events directory
    if [ ! -d "$TEST_EVENTS_DIR" ]; then
        echo "Error: Test events directory '$TEST_EVENTS_DIR' not found!"
        exit 1
    fi

    echo "Testing all events in $TEST_EVENTS_DIR..."
    for event_file in "$TEST_EVENTS_DIR"/*.xml; do
        if [ -f "$event_file" ]; then
            send_event_file "$event_file"
            sleep 1  # Small delay between events
        fi
    done
fi

echo ""
echo "Test completed!"

# Wait a moment for processing
echo "Waiting for Lambda processing..."
sleep 5

# Check Lambda logs
echo "Checking Lambda logs..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT logs describe-log-groups \
    --region $AWS_REGION || echo "No log groups found"

# Try to get recent logs
LOG_GROUP="/aws/lambda/agent-event-processor"
aws --endpoint-url=$LOCALSTACK_ENDPOINT logs describe-log-streams \
    --log-group-name $LOG_GROUP \
    --region $AWS_REGION \
    --order-by LastEventTime \
    --descending \
    --max-items 1 || echo "No log streams found"

echo "Test completed!"
echo ""
echo "To manually invoke the Lambda function:"
echo "aws --endpoint-url=$LOCALSTACK_ENDPOINT lambda invoke \\"
echo "    --function-name agent-event-processor \\"
echo "    --payload '{\"Records\":[{\"body\":\"{\\\"Message\\\":\\\"$TEST_XML\\\"}\"}]}' \\"
echo "    --region $AWS_REGION \\"
echo "    response.json"

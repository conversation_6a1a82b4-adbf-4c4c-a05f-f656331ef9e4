["lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_batch_event_processing", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_busied_out_event_processing", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_database_connectivity", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_invalid_xml_handling", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_response_format", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_timeout_handling", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_login_event_processing", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_missing_required_fields", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_insert_fact_record_duplicate", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_insert_fact_record_success", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_failure", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_success", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_resolve_dimension_keys", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_transform_to_fact_data", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_acd_login_event_processing", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_batch_processing_multiple_events", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_empty_records", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_invalid_xml_handling", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_partial_batch_failure", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_sns_wrapped_message", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_successful_login_event_processing", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_acd_event_validation", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_extra_fields_rejected", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_invalid_agent_uri", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_missing_required_fields", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_timestamp_parsing", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_valid_acd_login_event", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_valid_login_event", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDatabaseCredentials::test_empty_host", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDatabaseCredentials::test_invalid_port", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDatabaseCredentials::test_valid_credentials", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDimensionKeys::test_invalid_date_key", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDimensionKeys::test_invalid_time_key", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDimensionKeys::test_valid_dimension_keys", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_agent_busied_out_event_to_fact_data", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_login_event_to_fact_data", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_logout_event_with_voice_qos", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_queue_state_change_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_extract_xml_from_raw_sqs_message", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_extract_xml_from_sqs_json_message", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_acd_login_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_agent_available_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_agent_busied_out_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_invalid_xml", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_login_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_logout_event_with_voice_qos", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_queue_state_change_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_unknown_event_type", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_xml_missing_required_fields"]
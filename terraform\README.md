# Smart Analytics Processors - Terraform Infrastructure

This directory contains the Terraform infrastructure code for deploying AWS Lambda functions that process smart analytics data. The infrastructure supports multi-customer deployments with per-customer isolation and monitoring.

## Architecture Overview

```mermaid
graph TB
    subgraph "AWS Account"
        subgraph "VPC"
            subgraph "Private Subnets"
                Lambda[Lambda Function<br/>ACD Processor]
                Redshift[(Redshift Cluster)]
            end
        end
        
        subgraph "SQS"
            Queue[Processing Queue]
            DLQ[Dead Letter Queue]
        end
        
        subgraph "S3"
            CodeBucket[Lambda Code Bucket<br/>KMS Encrypted]
        end
        
        subgraph "Secrets Manager"
            RedshiftCreds[Redshift Credentials]
        end
        
        subgraph "CloudWatch"
            Logs[Log Groups]
            Alarms[Metric Alarms]
        end
        
        subgraph "SNS"
            AlertTopic[Slack Alerts Topic]
        end
    end
    
    Queue --> Lambda
    DLQ --> Lambda
    Lambda --> Redshift
    Lambda --> Logs
    CodeBucket --> Lambda
    RedshiftCreds --> Lambda
    Alarms --> AlertTopic
```

## Directory Structure

```
terraform/
├── main.tf                # Root module configuration
├── variables.tf           # Input variables
├── locals.tf              # Local values
├── data.tf                # Data sources
├── outputs.tf             # Output values
├── versions.tf            # Provider requirements
├── README.md              # This file
├── modules/
│   ├── acd-processor/     # ACD processor Lambda module
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   ├── outputs.tf
│   │   ├── lambda.tf      # Lambda function resources
│   │   ├── iam.tf         # IAM roles and policies
│   │   ├── data.tf        # Data sources
│   │   └── README.md      # Module documentation
│   └── alarms/            # CloudWatch alarms module
│       ├── main.tf
│       ├── variables.tf
│       ├── outputs.tf
│       ├── cloudwatch.tf  # CloudWatch alarm resources
│       ├── data.tf        # Data sources
│       └── README.md      # Module documentation
```

## Features

### Multi-Customer Support
- Per-customer Lambda function deployment
- Isolated resources with customer-specific naming
- Configurable per-customer settings

### Security
- VPC deployment for secure Redshift access
- KMS encryption for S3 objects
- Least-privilege IAM permissions
- Secrets Manager integration

### Monitoring & Alerting
- CloudWatch alarms
- Error detection and alerting
- Performance monitoring
- Anomaly detection for invocation patterns

### Scalability
- Configurable Lambda concurrency limits
- SQS batch processing optimization
- Reserved concurrency for predictable performance

## Usage

### Prerequisites

1. **AWS CLI configured** with appropriate permissions
2. **Terraform >= 1.6** installed
3. **S3 bucket** for Lambda code (created in separate repository)
4. **KMS key** for encryption
5. **VPC and subnets** configured
6. **Redshift cluster** deployed
7. **SNS topic** for alerts

## Configuration

### Customer Configuration

Each customer requires the following configuration in `service_customer_config`:

- **S3 bucket name** for Lambda code storage
- **KMS key** for encryption
- **ACD settings** including queues, VPC, and Redshift details
- **Environment variables** for Lambda function
- **Tags** for resource identification

### Lambda Configuration

Key Lambda settings that can be tuned:

- **Memory**: 128MB to 2048MB (default: 256MB)
- **Timeout**: 1 to 900 seconds (default: 60s)
- **Reserved Concurrency**: -1 (unreserved) or positive number
- **Max Concurrency**: 2 to 1000 (default: 10)

### SQS Configuration

- **Batch Size**: 1 to 10,000 messages (default: 10)
- **Batching Window**: 0 to 300 seconds (default: 0)

## Monitoring

### CloudWatch Alarms

Each Lambda function gets three alarms:

1. **Errors**: Triggers on any function errors
2. **Duration**: Warns when execution time > 10 seconds
3. **Invocations**: Anomaly detection for unusual patterns

### Logs

- CloudWatch log groups with 1-year retention
- Structured logging for better searchability
- Log group per Lambda function

## Security Considerations

### IAM Permissions

The Lambda execution role includes minimal permissions for:
- Reading from SQS queues
- Writing to CloudWatch Logs
- Accessing Secrets Manager
- Connecting to Redshift via VPC
- Decrypting KMS-encrypted objects

### Network Security

- Lambda functions deployed in private subnets
- Security groups restrict network access
- VPC endpoints for AWS services (recommended)

### Data Encryption

- S3 objects encrypted with customer-managed KMS keys
- Secrets Manager for database credentials
- TLS for all data in transit
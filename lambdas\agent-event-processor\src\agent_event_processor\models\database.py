"""
Database models and credentials for Redshift connectivity.

This module defines data models for database credentials and connection
management with proper type safety.
"""

from typing import Optional

from pydantic import BaseModel, Field, field_validator


class DatabaseCredentials(BaseModel):
    """
    Database credentials retrieved from AWS Secrets Manager.

    This model validates and structures database connection credentials
    for secure Redshift connectivity.
    """

    host: str = Field(..., description="Database host endpoint")
    port: int = Field(..., description="Database port number")
    database: str = Field(..., description="Database name")
    username: str = Field(..., description="Database username")
    password: str = Field(..., description="Database password")
    engine: Optional[str] = Field(
        default="redshift", description="Database engine type"
    )

    @field_validator("port")
    @classmethod
    def validate_port(cls, v: int) -> int:
        """Validate database port is in valid range."""
        if not (1 <= v <= 65535):
            raise ValueError("Port must be between 1 and 65535")
        return v

    @field_validator("host")
    @classmethod
    def validate_host(cls, v: str) -> str:
        """Validate database host format."""
        if not v or v.isspace():
            raise ValueError("Host cannot be empty")
        return v.strip()


class DimensionKeys(BaseModel):
    """
    Dimension keys for fact table relationships.

    This model represents the foreign keys needed for fact table inserts
    with proper validation.
    """

    tenant_key: int = Field(..., description="Foreign key to dim_tenant")
    agent_key: int = Field(..., description="Foreign key to dim_agent")
    date_key: int = Field(..., description="Foreign key to dim_date (YYYYMMDD format)")
    time_key: int = Field(..., description="Foreign key to dim_time (HHMMSS format)")
    queue_key: Optional[int] = Field(
        None, description="Foreign key to dim_queue (for ACD events)"
    )

    @field_validator("date_key")
    @classmethod
    def validate_date_key(cls, v: int) -> int:
        """Validate date key format (YYYYMMDD)."""
        if not (19000101 <= v <= 22001231):
            raise ValueError(
                "Date key must be in YYYYMMDD format between 1900-01-01 and 2200-12-31"
            )
        return v

    @field_validator("time_key")
    @classmethod
    def validate_time_key(cls, v: int) -> int:
        """Validate time key format (HHMMSS)."""
        if not (0 <= v <= 235959):
            raise ValueError("Time key must be in HHMMSS format (000000-235959)")
        return v

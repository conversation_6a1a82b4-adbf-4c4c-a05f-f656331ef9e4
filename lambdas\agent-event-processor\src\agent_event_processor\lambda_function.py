"""
Main Lambda handler for processing agent events from SQS.

This module provides the entry point for the AWS Lambda function that processes
agent events from SQS queues and populates Redshift dimension tables.
"""

import json
import time
from typing import Any, Dict, Optional

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.metrics import MetricUnit
from aws_lambda_typing import context as LambdaContext, events

from .config import Settings, get_settings
from .models.events import AgentEvent
from .services.event_processor import EventProcessor
from .utils.xml_parser import AgentEventXMLParser

# Global variables for Lambda container reuse - initialized lazily
_settings: Optional[Settings] = None
_event_processor: Optional[EventProcessor] = None
_metrics: Optional[Metrics] = None

# Initialize Lambda Powertools
logger = Logger(service="agent-event-processor")
tracer = Tracer(service="agent-event-processor")


def _get_settings() -> Settings:
    """Get cached settings instance with lazy initialization."""
    global _settings
    if _settings is None:
        _settings = get_settings()
        logger.debug("Settings initialized", environment=_settings.environment)
    return _settings


def _get_metrics() -> Metrics:
    """Get cached metrics instance with lazy initialization."""
    global _metrics
    if _metrics is None:
        settings = _get_settings()
        _metrics = Metrics(namespace=settings.aws.metrics_namespace)
        logger.debug("Metrics initialized", namespace=settings.aws.metrics_namespace)
    return _metrics


def _prepare_event_data(json_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare hierarchical JSON data for the AgentEvent model with validation.

    Args:
        json_data: Hierarchical JSON data from XML conversion

    Returns:
        Data structured for AgentEvent with preserved hierarchy in event_data

    Raises:
        ValueError: If data structure is invalid
    """
    if not isinstance(json_data, dict):
        raise ValueError("JSON data must be a dictionary")

    # Define core fields that belong at the top level
    core_fields = {"timestamp", "eventType", "agencyOrElement", "agent"}

    # Extract core fields
    event_fields = {}
    event_data = {}

    # Separate core fields from event-specific data
    for key, value in json_data.items():
        # Skip XML namespace attributes
        if key.startswith("@"):
            continue

        if key in core_fields:
            event_fields[key] = value
        else:
            event_data[key] = value

    # Combine core fields with event_data
    result = event_fields.copy()
    result["event_data"] = event_data
    logger.info("Event data prepared for processing", result=result)
    return result


def get_event_processor() -> EventProcessor:
    """
    Get or create event processor instance (singleton for container reuse).

    Returns:
        EventProcessor: Configured event processor instance.
    """
    global _event_processor

    if _event_processor is None:
        settings = _get_settings()
        _event_processor = EventProcessor(settings)
        logger.info("Event processor initialized")

    return _event_processor


def _process_single_record(
    record: Dict[str, Any], processor: EventProcessor, context: LambdaContext
) -> Dict[str, Any]:
    """
    Process a single SQS record.

    Args:
        record: SQS record to process
        processor: Event processor instance
        context: Lambda context

    Returns:
        Dict with processing result

    Raises:
        Exception: If processing fails
    """
    # Validate record size
    record_body = record.get("body", "")
    # Extract and convert XML to JSON
    xml_content = AgentEventXMLParser.extract_xml_from_sqs_message(record_body)
    json_data = AgentEventXMLParser.xml_to_json(xml_content)

    # Prepare hierarchical data for AgentEvent model
    event_data = _prepare_event_data(json_data)

    # Validate with Pydantic model
    agent_event = AgentEvent.model_validate(event_data)

    # Add SQS metadata
    agent_event.sqs_message_id = record.get("messageId")
    agent_event.sqs_receipt_handle = record.get("receiptHandle")

    # Process the event (store to database)
    processor.process_single_event(agent_event, context)

    return {
        "success": True,
        "event_type": agent_event.event_type,
        "agent": agent_event.agent,
        "message_id": record.get("messageId"),
    }


@logger.inject_lambda_context
@tracer.capture_lambda_handler
def lambda_handler(event: events.SQSEvent, context: LambdaContext) -> Dict[str, Any]:
    """
    Main Lambda handler for processing agent events from SQS.

    This function processes batches of agent events from SQS, validates them,
    and populates the appropriate Redshift dimension and fact tables.

    Args:
        event: SQS event containing Records array with agent events.
        context: Lambda execution context with request metadata.

    Returns:
        Dict containing processing results and any failed message identifiers
        for partial batch failure handling.

    Raises:
        Exception: Re-raises any unhandled exceptions after logging.
    """
    start_time = time.time()

    try:
        settings = _get_settings()
        metrics = _get_metrics()

        records = event.get("Records", [])

        logger.info(
            "Processing SQS batch",
            record_count=len(records),
            memory_limit_mb=context.memory_limit_in_mb,
            remaining_time_ms=context.get_remaining_time_in_millis(),
        )

        # Initialize counters and tracking
        successful_count, failed_count = 0, 0
        failed_message_ids = []
        # Get event processor
        processor = get_event_processor()

        for _, record in enumerate(records):
            try:
                result = _process_single_record(record, processor, context)
                successful_count += 1
                logger.info(
                    "Successfully processed event",
                    event_type=result["event_type"],
                    agent=result["agent"],
                    message_id=result["message_id"],
                )

            except Exception as e:
                failed_count += 1
                message_id = record.get("messageId", "unknown")
                failed_message_ids.append(message_id)

                logger.error(
                    "Failed to process record",
                    error=str(e),
                    message_id=message_id,
                    record_size=len(record.get("body", "")),
                    exc_info=True,
                )

        processing_time = (time.time() - start_time) * 1000

        if settings.enable_custom_metrics:
            metrics.add_metric(
                name="PartialBatchFailures",
                unit=MetricUnit.Count,
                value=1 if failed_count > 0 else 0,
            )

        if settings.enable_business_metrics and successful_count > 0:
            metrics.add_metric(
                name="EventsProcessedSuccessfully",
                unit=MetricUnit.Count,
                value=successful_count,
            )

        if failed_count > 0:
            logger.warning(
                "Batch processing completed with failures",
                successful_count=successful_count,
                failed_count=failed_count,
                failed_message_ids=failed_message_ids,
                processing_time_ms=processing_time,
                batch_size=len(records),
                failure_rate=failed_count / len(records) if len(records) > 0 else 0,
            )
        else:
            logger.info(
                "Batch processing completed successfully",
                successful_count=successful_count,
                processing_time_ms=processing_time,
                batch_size=len(records),
            )

        batch_item_failures = [
            {"itemIdentifier": msg_id} for msg_id in failed_message_ids
        ]

        response = {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "message": "Batch processing completed",
                    "successful_count": successful_count,
                    "failed_count": failed_count,
                    "processing_time_ms": processing_time,
                }
            ),
        }

        if batch_item_failures:
            response["batchItemFailures"] = batch_item_failures

        metrics.flush_metrics(raise_on_empty_metrics=False)
        return response

    except Exception as e:
        # Log the error with full context
        processing_time = (time.time() - start_time) * 1000
        logger.error(
            "Lambda execution failed",
            error=str(e),
            error_type=type(e).__name__,
            processing_time_ms=processing_time,
            exc_info=True,
        )

        settings = _get_settings()
        metrics = _get_metrics()
        if settings.enable_custom_metrics:
            metrics.add_metric(
                name="CriticalProcessingErrors", unit=MetricUnit.Count, value=1
            )

        metrics.flush_metrics(raise_on_empty_metrics=False)

        # Re-raise to trigger Lambda error handling
        raise

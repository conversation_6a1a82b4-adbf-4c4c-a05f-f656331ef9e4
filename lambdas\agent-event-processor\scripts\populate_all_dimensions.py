#!/usr/bin/env python3
"""
Populate all dimension tables with comprehensive data.

This script populates:
- dim_date: All dates for current year ± 1 year
- dim_time: All possible time combinations (every second)
"""

import os
import sys
import psycopg2
from datetime import datetime, timedelta
from typing import List, Tuple


def get_db_connection():
    """Get database connection for local PostgreSQL."""
    return psycopg2.connect(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", 5432)),
        database=os.getenv("DB_NAME", "acd"),
        user=os.getenv("DB_USER", "test_user"),
        password=os.getenv("DB_PASSWORD", "test_password"),
    )


def populate_date_dimension(conn) -> None:
    """Populate dim_date table with comprehensive date range."""
    print("Populating dim_date table...")

    with conn.cursor() as cursor:
        # Generate dates for current year ± 1 year
        start_date = datetime.now() - timedelta(days=365)
        end_date = datetime.now() + timedelta(days=365)

        current_date = start_date
        batch_size = 100
        dates_batch = []

        while current_date <= end_date:
            date_key = int(current_date.strftime("%Y%m%d"))

            dates_batch.append(
                (
                    date_key,
                    current_date.date(),
                    current_date.year,
                    ((current_date.month - 1) // 3) + 1,  # Quarter
                    current_date.month,
                    current_date.day,
                    current_date.weekday(),  # 0=Monday
                    current_date.strftime("%A"),
                    current_date.strftime("%B"),
                    current_date.weekday() >= 5,  # Weekend
                )
            )

            if len(dates_batch) >= batch_size:
                insert_date_batch(cursor, dates_batch)
                dates_batch = []

            current_date += timedelta(days=1)

        # Insert remaining dates
        if dates_batch:
            insert_date_batch(cursor, dates_batch)

        conn.commit()
        print(
            f"Populated dim_date with dates from {start_date.date()} to {end_date.date()}"
        )


def insert_date_batch(cursor, dates_batch: List[Tuple]) -> None:
    """Insert a batch of dates."""
    insert_sql = """
        INSERT INTO dim_date (
            date_key, full_date, year, quarter, month, day, 
            day_of_week, day_name, month_name, is_weekend
        ) VALUES %s
        ON CONFLICT (date_key) DO NOTHING
    """

    from psycopg2.extras import execute_values

    execute_values(cursor, insert_sql, dates_batch)


def populate_time_dimension(conn) -> None:
    """Populate dim_time table with all possible times (every second)."""
    print("Populating dim_time table with all possible times...")

    with conn.cursor() as cursor:
        batch_size = 1000
        times_batch = []

        # Generate all times for 24 hours (every second)
        for hour in range(24):
            for minute in range(60):
                for second in range(60):
                    time_key = hour * 10000 + minute * 100 + second

                    # Determine time of day
                    if hour == 0:
                        time_of_day = "Midnight"
                    elif hour < 6:
                        time_of_day = "Early Morning"
                    elif hour < 12:
                        time_of_day = "Morning"
                    elif hour == 12:
                        time_of_day = "Noon"
                    elif hour < 18:
                        time_of_day = "Afternoon"
                    elif hour < 22:
                        time_of_day = "Evening"
                    else:
                        time_of_day = "Night"

                    # Hour name
                    if hour == 0:
                        hour_name = "12 AM"
                    elif hour < 12:
                        hour_name = f"{hour} AM"
                    elif hour == 12:
                        hour_name = "12 PM"
                    else:
                        hour_name = f"{hour - 12} PM"

                    times_batch.append(
                        (time_key, hour, minute, second, time_of_day, hour_name)
                    )

                    if len(times_batch) >= batch_size:
                        insert_time_batch(cursor, times_batch)
                        times_batch = []

        # Insert remaining times
        if times_batch:
            insert_time_batch(cursor, times_batch)

        conn.commit()
        print("Populated dim_time with all possible times (86,400 records)")


def insert_time_batch(cursor, times_batch: List[Tuple]) -> None:
    """Insert a batch of times."""
    insert_sql = """
        INSERT INTO dim_time (
            time_key, hour, minute, second, time_of_day, hour_name
        ) VALUES %s
        ON CONFLICT (time_key) DO NOTHING
    """

    from psycopg2.extras import execute_values

    execute_values(cursor, insert_sql, times_batch)


def verify_dimensions(conn) -> None:
    """Verify dimension table population."""
    print("\nVerifying dimension tables...")

    with conn.cursor() as cursor:
        # Check dim_date
        cursor.execute("SELECT COUNT(*) FROM dim_date")
        date_count = cursor.fetchone()[0]

        # Check dim_time
        cursor.execute("SELECT COUNT(*) FROM dim_time")
        time_count = cursor.fetchone()[0]

        # Check dim_tenant
        cursor.execute("SELECT COUNT(*) FROM dim_tenant")
        tenant_count = cursor.fetchone()[0]

        # Check dim_agent
        cursor.execute("SELECT COUNT(*) FROM dim_agent")
        agent_count = cursor.fetchone()[0]

        # Check dim_queue
        cursor.execute("SELECT COUNT(*) FROM dim_queue")
        queue_count = cursor.fetchone()[0]

        print(f"Dimension table counts:")
        print(f"  dim_date: {date_count:,} records")
        print(f"  dim_time: {time_count:,} records")
        print(f"  dim_tenant: {tenant_count:,} records")
        print(f"  dim_agent: {agent_count:,} records")
        print(f"  dim_queue: {queue_count:,} records")


def main():
    """Main function to populate all dimension tables."""
    try:
        print("Starting comprehensive dimension table population...")

        # Connect to database
        conn = get_db_connection()
        print("Connected to database")

        # Populate dimension tables
        populate_date_dimension(conn)
        populate_time_dimension(conn)

        # Verify data
        verify_dimensions(conn)

        conn.close()
        print("\nDimension population completed successfully!")

    except Exception as e:
        print(f"Error populating dimensions: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

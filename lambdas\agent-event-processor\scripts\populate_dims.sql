-- Populate dimension tables for testing

-- Populate dim_date with dates for 2025
INSERT INTO dim_date (date_key, full_date, year, quarter, month, day, day_of_week, day_name, month_name, is_weekend)
SELECT
    CAST(TO_CHAR(date_series, 'YYYYMMDD') AS INTEGER) as date_key,
    date_series::date as full_date,
    EXTRACT(YEAR FROM date_series) as year,
    EXTRACT(QUARTER FROM date_series) as quarter,
    EXTRACT(MONTH FROM date_series) as month,
    EXTRACT(DAY FROM date_series) as day,
    EXTRACT(DOW FROM date_series) as day_of_week,
    TRIM(TO_CHAR(date_series, 'Day')) as day_name,
    TRIM(TO_CHAR(date_series, 'Month')) as month_name,
    EXTRACT(DOW FROM date_series) IN (0, 6) as is_weekend
FROM generate_series('2025-01-01'::date, '2025-12-31'::date, '1 day'::interval) as date_series
ON CONFLICT (date_key) DO NOTHING;

-- Populate dim_time with time intervals
INSERT INTO dim_time (time_key, hour, minute, second, time_of_day, hour_name)
SELECT
    hour * 10000 + minute * 100 + second as time_key,
    hour,
    minute,
    second,
    CASE
        WHEN hour = 0 THEN 'Midnight'
        WHEN hour < 6 THEN 'Early Morning'
        WHEN hour < 12 THEN 'Morning'
        WHEN hour = 12 THEN 'Noon'
        WHEN hour < 18 THEN 'Afternoon'
        WHEN hour < 22 THEN 'Evening'
        ELSE 'Night'
    END as time_of_day,
    CASE
        WHEN hour = 0 THEN '12 AM'
        WHEN hour < 12 THEN hour || ' AM'
        WHEN hour = 12 THEN '12 PM'
        ELSE (hour - 12) || ' PM'
    END as hour_name
FROM
    generate_series(0, 23) as hour,
    generate_series(0, 59, 15) as minute,  -- Every 15 minutes
    generate_series(0, 59, 30) as second   -- Every 30 seconds
ON CONFLICT (time_key) DO NOTHING;

-- Verify the data
SELECT 'dim_date count:' as table_name, COUNT(*) as record_count FROM dim_date
UNION ALL
SELECT 'dim_time count:' as table_name, COUNT(*) as record_count FROM dim_time;

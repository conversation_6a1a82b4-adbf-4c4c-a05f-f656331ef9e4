"""
Unit tests for XML parser utilities.

This module tests the XML parsing functionality for agent events,
ensuring proper conversion from XML to structured data.
"""

import pytest
from src.agent_event_processor.utils.xml_parser import AgentEventXMLParser


class TestAgentEventXMLParser:
    """Test suite for AgentEventXMLParser."""

    def test_parse_login_event(self):
        """Test parsing of login event XML."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2025-02-05T11:44:18.031Z</timestamp>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>dycj</agent>
            <eventType>Login</eventType>
            <login>
                <mediaLabel>_ML_194D5ECDE50C0001C46A@BrandonMB</mediaLabel>
                <uri>tel:+2045553006</uri>
                <agentRole>Rural - CT</agentRole>
                <tenantGroup>Brandon911</tenantGroup>
                <operatorId>6</operatorId>
                <workstation>OP6</workstation>
                <deviceName>Headset</deviceName>
                <reason>normal</reason>
            </login>
        </LogEvent>
        """

        result = AgentEventXMLParser.xml_to_json(xml_content)

        assert result["timestamp"] == "2025-02-05T11:44:18.031Z"
        assert result["agencyOrElement"] == "Brandon911"
        assert result["agent"] == "dycj"
        assert result["eventType"] == "Login"
        assert result["login"]["mediaLabel"] == "_ML_194D5ECDE50C0001C46A@BrandonMB"
        assert result["login"]["uri"] == "tel:+2045553006"
        assert result["login"]["agentRole"] == "Rural - CT"
        assert result["login"]["tenantGroup"] == "Brandon911"
        assert result["login"]["operatorId"] == "6"
        assert result["login"]["workstation"] == "OP6"
        assert result["login"]["deviceName"] == "Headset"
        assert result["login"]["reason"] == "normal"

    def test_parse_logout_event_with_voice_qos(self):
        """Test parsing of logout event with voice quality metrics."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2025-02-05T11:46:03.421Z</timestamp>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>lacd</agent>
            <eventType>Logout</eventType>
            <logout>
                <mediaLabel>_ML_194D357A8C8C0001C329@BrandonMB</mediaLabel>
                <uri>tel:+2045553007</uri>
                <agentRole>BPS - CT_Dispatch</agentRole>
                <tenantGroup>Brandon911</tenantGroup>
                <operatorId>7</operatorId>
                <workstation>OP7</workstation>
                <deviceName>Headset</deviceName>
                <reason>normal</reason>
                <responseCode>16</responseCode>
                <voiceQOS>
                    <mediaIpSourceAddr>*************</mediaIpSourceAddr>
                    <mediaIpDestAddr>**************</mediaIpDestAddr>
                    <mediaUdpRtpSourcePort>21464</mediaUdpRtpSourcePort>
                    <mediaUdpRtpDestPort>4000</mediaUdpRtpDestPort>
                    <mediaNumOfRtpPktRxed>2018682</mediaNumOfRtpPktRxed>
                    <mediaNumOfRtpPktTxed>2171890</mediaNumOfRtpPktTxed>
                    <mediaRtpJitter>6</mediaRtpJitter>
                    <mediaRtpLatency>1</mediaRtpLatency>
                </voiceQOS>
            </logout>
        </LogEvent>
        """

        result = AgentEventXMLParser.xml_to_json(xml_content)

        assert result["eventType"] == "Logout"
        assert result["agent"] == "lacd"
        assert result["logout"]["responseCode"] == "16"
        assert "voiceQOS" in result["logout"]

        voice_qos = result["logout"]["voiceQOS"]
        assert voice_qos["mediaIpSourceAddr"] == "*************"
        assert voice_qos["mediaIpDestAddr"] == "**************"
        assert voice_qos["mediaUdpRtpSourcePort"] == "21464"
        assert voice_qos["mediaUdpRtpDestPort"] == "4000"
        assert voice_qos["mediaNumOfRtpPktRxed"] == "2018682"
        assert voice_qos["mediaNumOfRtpPktTxed"] == "2171890"
        assert voice_qos["mediaRtpJitter"] == "6"
        assert voice_qos["mediaRtpLatency"] == "1"

    def test_parse_acd_login_event(self):
        """Test parsing of ACD login event."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2015-08-21T12:58:03.01Z</timestamp>
            <agencyOrElement>chicago.psap.il.us</agencyOrElement>
            <agent>james.smith</agent>
            <eventType>ACDLogin</eventType>
            <acdLogin>
                <mediaLabel><EMAIL></mediaLabel>
                <agentUri>tel:+6432341234</agentUri>
                <agentRole>operator</agentRole>
                <tenantGroup>chicago.psap.il.us</tenantGroup>
                <operatorId>001</operatorId>
                <workstation>PC Host name</workstation>
                <deviceName>Headset</deviceName>
                <ringGroupName>911 Queue</ringGroupName>
                <ringGroupUri>tel:+6432341234</ringGroupUri>
            </acdLogin>
        </LogEvent>
        """

        result = AgentEventXMLParser.xml_to_json(xml_content)

        assert result["eventType"] == "ACDLogin"
        assert result["agent"] == "james.smith"
        assert result["agencyOrElement"] == "chicago.psap.il.us"
        assert result["acdLogin"]["agentUri"] == "tel:+6432341234"
        assert result["acdLogin"]["ringGroupName"] == "911 Queue"
        assert result["acdLogin"]["ringGroupUri"] == "tel:+6432341234"

    def test_parse_agent_available_event(self):
        """Test parsing of agent available event."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2025-02-05T10:20:00.386Z</timestamp>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>trea</agent>
            <eventType>AgentAvailable</eventType>
            <agentAvailable>
                <mediaLabel>_ML_194D3628220C0001C336@BrandonMB</mediaLabel>
                <uri>tel:+2045553001</uri>
                <agentRole>911 Rural PE</agentRole>
                <tenantGroup>Brandon911</tenantGroup>
                <operatorId>1</operatorId>
                <workstation>OP1</workstation>
                <busiedOutAction>Manual</busiedOutAction>
            </agentAvailable>
        </LogEvent>
        """

        result = AgentEventXMLParser.xml_to_json(xml_content)

        assert result["eventType"] == "AgentAvailable"
        assert result["agent"] == "trea"
        assert result["agentAvailable"]["busiedOutAction"] == "Manual"

    def test_parse_agent_busied_out_event(self):
        """Test parsing of agent busied out event."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2025-02-05T14:34:08.951Z</timestamp>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>keem</agent>
            <eventType>AgentBusiedOut</eventType>
            <agentbusiedout>
                <mediaLabel>_ML_194D5F04D38C0001C46D@BrandonMB</mediaLabel>
                <uri>tel:+2045553007</uri>
                <agentRole>BPS - CT_Dispatch</agentRole>
                <tenantGroup>Brandon911</tenantGroup>
                <operatorId>7</operatorId>
                <workstation>OP7</workstation>
                <reason>break</reason>
                <action>manual</action>
                <duration>900</duration>
            </agentbusiedout>
        </LogEvent>
        """

        result = AgentEventXMLParser.xml_to_json(xml_content)

        assert result["eventType"] == "AgentBusiedOut"
        assert result["agent"] == "keem"
        assert result["agentbusiedout"]["reason"] == "break"
        assert result["agentbusiedout"]["action"] == "manual"
        assert result["agentbusiedout"]["duration"] == "900"

    def test_parse_queue_state_change_event(self):
        """Test parsing of queue state change event."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2015-08-21T12:58:03.01Z</timestamp>
            <agencyOrElement>esrp.state.il.us</agencyOrElement>
            <agent>.</agent>
            <callIdentifier>.</callIdentifier>
            <incidentIdentifier>.</incidentIdentifier>
            <eventType>QueueStateChange</eventType>
            <QueueStateChange>
                <StateChangeNotificationContents>Count</StateChangeNotificationContents>
                <queueId>2470001234</queueId>
                <queueName>2470001234</queueName>
                <direction></direction>
                <count>1</count>
            </QueueStateChange>
        </LogEvent>
        """

        result = AgentEventXMLParser.xml_to_json(xml_content)

        assert result["eventType"] == "QueueStateChange"
        assert result["agent"] == "."
        assert result["QueueStateChange"]["queueId"] == "2470001234"
        assert result["QueueStateChange"]["queueName"] == "2470001234"
        assert result["QueueStateChange"]["count"] == "1"

    def test_extract_xml_from_sqs_json_message(self):
        """Test extracting XML from JSON-wrapped SQS message."""
        sqs_body = """
        {
            "Message": "<LogEvent xmlns=\\"http://solacom.com/Logging\\"><timestamp>2025-02-05T11:44:18.031Z</timestamp><agencyOrElement>Brandon911</agencyOrElement><agent>test</agent><eventType>Login</eventType></LogEvent>"
        }
        """

        xml_content = AgentEventXMLParser.extract_xml_from_sqs_message(sqs_body)

        assert xml_content.startswith('<LogEvent xmlns="http://solacom.com/Logging">')
        assert "test" in xml_content

    def test_extract_xml_from_raw_sqs_message(self):
        """Test extracting XML from raw SQS message."""
        sqs_body = '<LogEvent xmlns="http://solacom.com/Logging"><timestamp>2025-02-05T11:44:18.031Z</timestamp></LogEvent>'

        xml_content = AgentEventXMLParser.extract_xml_from_sqs_message(sqs_body)

        assert xml_content == sqs_body

    def test_parse_invalid_xml(self):
        """Test parsing of invalid XML."""
        xml_content = "<invalid><xml>"

        with pytest.raises(ValueError, match="Failed to parse XML"):
            AgentEventXMLParser.xml_to_json(xml_content)

    def test_parse_xml_missing_required_fields(self):
        """Test parsing XML with missing required fields."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2025-02-05T11:44:18.031Z</timestamp>
            <agencyOrElement>Brandon911</agencyOrElement>
            <!-- Missing agent and eventType -->
        </LogEvent>
        """

        with pytest.raises(ValueError, match="Missing required fields"):
            AgentEventXMLParser.xml_to_json(xml_content)

    def test_parse_unknown_event_type(self):
        """Test parsing XML with unknown event type."""
        xml_content = """
        <LogEvent xmlns="http://solacom.com/Logging">
            <timestamp>2025-02-05T11:44:18.031Z</timestamp>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>test</agent>
            <eventType>UnknownEvent</eventType>
        </LogEvent>
        """

        result = AgentEventXMLParser.xml_to_json(xml_content)

        # Should still parse basic fields even with unknown event type
        assert result["eventType"] == "UnknownEvent"
        assert result["agent"] == "test"

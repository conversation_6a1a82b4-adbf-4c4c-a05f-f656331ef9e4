#!/bin/bash
# Setup script for LocalStack environment
# This script creates the necessary AWS resources for local testing

set -e

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
AWS_REGION="us-east-1"
QUEUE_NAME="agent-events-queue"
DLQ_NAME="agent-events-dlq"
SECRET_NAME="agent-event-processor/database"

echo "Setting up LocalStack environment for Agent Event Processor..."

# Wait for LocalStack to be ready
echo "Waiting for LocalStack to be ready..."
until curl -s "$LOCALSTACK_ENDPOINT/_localstack/health" | grep -q '"sqs": "available"'; do
    echo "Waiting for LocalStack..."
    sleep 2
done
echo "LocalStack is ready!"

# Configure AWS CLI for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=$AWS_REGION

# Create SQS Dead Letter Queue
echo "Creating SQS Dead Letter Queue..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue \
    --queue-name $DLQ_NAME \
    --region $AWS_REGION

DLQ_URL=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url \
    --queue-name $DLQ_NAME \
    --region $AWS_REGION \
    --query 'QueueUrl' --output text)

DLQ_ARN=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-attributes \
    --queue-url $DLQ_URL \
    --attribute-names QueueArn \
    --region $AWS_REGION \
    --query 'Attributes.QueueArn' --output text)

echo "Created DLQ: $DLQ_ARN"

# Create main SQS Queue with DLQ configuration
echo "Creating main SQS Queue..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue \
    --queue-name $QUEUE_NAME \
    --region $AWS_REGION \
    --attributes '{
        "VisibilityTimeoutSeconds": "300",
        "MessageRetentionPeriod": "1209600",
        "RedrivePolicy": "{\"deadLetterTargetArn\":\"'$DLQ_ARN'\",\"maxReceiveCount\":3}"
    }'

QUEUE_URL=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url \
    --queue-name $QUEUE_NAME \
    --region $AWS_REGION \
    --query 'QueueUrl' --output text)

echo "Created main queue: $QUEUE_URL"

# Create SSM Parameter Store parameter for database credentials
echo "Creating SSM Parameter Store parameter..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT ssm put-parameter \
    --name $SECRET_NAME \
    --value '{"host":"postgres","port":5432,"database":"acd","username":"test_user","password":"test_password"}' \
    --type SecureString \
    --region $AWS_REGION

echo "Created secret: $SECRET_NAME"

# Create CloudWatch Log Group
echo "Creating CloudWatch Log Group..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT logs create-log-group \
    --log-group-name "/aws/lambda/agent-event-processor" \
    --region $AWS_REGION

echo "Created log group: /aws/lambda/agent-event-processor"

# Build and deploy Lambda function
echo "Building Lambda function..."
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Build the Lambda package
bash "$SCRIPT_DIR/build_lambda.sh"

# Deploy Lambda function
echo "Deploying Lambda function to LocalStack..."
LAMBDA_ZIP="$PROJECT_DIR/../../smart-analytics-acd-processor.zip"

if [ ! -f "$LAMBDA_ZIP" ]; then
    echo "Error: Lambda package not found at $LAMBDA_ZIP"
    exit 1
fi

aws --endpoint-url=$LOCALSTACK_ENDPOINT lambda create-function \
    --function-name agent-event-processor \
    --runtime python3.12 \
    --role arn:aws:iam::123456789012:role/lambda-execution-role \
    --handler lambda_function.lambda_handler \
    --zip-file fileb://"$LAMBDA_ZIP" \
    --timeout 300 \
    --memory-size 512 \
    --environment Variables="{REDSHIFT_SECRET_NAME=$SECRET_NAME,AWS_REGION=$AWS_REGION,ENABLE_CUSTOM_METRICS=false,ENABLE_BUSINESS_METRICS=false}" \
    --region $AWS_REGION

echo "Lambda function deployed successfully!"

# Get SQS queue ARN for event source mapping
QUEUE_ARN=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-attributes \
    --queue-url $QUEUE_URL \
    --attribute-names QueueArn \
    --region $AWS_REGION \
    --query 'Attributes.QueueArn' --output text)

# Create event source mapping (SQS trigger)
echo "Creating SQS trigger for Lambda function..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT lambda create-event-source-mapping \
    --event-source-arn $QUEUE_ARN \
    --function-name agent-event-processor \
    --batch-size 10 \
    --maximum-batching-window-in-seconds 5 \
    --region $AWS_REGION

echo "SQS trigger created successfully!"

echo ""
echo "LocalStack setup complete!"
echo ""
echo "Resources created:"
echo "  - SQS Queue: $QUEUE_URL"
echo "  - SQS DLQ: $DLQ_URL"
echo "  - Secret: $SECRET_NAME"
echo "  - Log Group: /aws/lambda/agent-event-processor"
echo "  - Lambda Function: agent-event-processor"
echo "  - SQS Trigger: $QUEUE_ARN -> agent-event-processor"
echo ""
echo "Environment variables for testing:"
echo "  export AWS_ENDPOINT_URL=$LOCALSTACK_ENDPOINT"
echo "  export REDSHIFT_SECRET_NAME=$SECRET_NAME"
echo "  export AWS_REGION=$AWS_REGION"
echo ""
echo "To send test messages:"
echo "  ./test_lambda.sh"
echo "  or"
echo "  aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs send-message \\"
echo "    --queue-url $QUEUE_URL \\"
echo "    --message-body '{\"Message\": \"<LogEvent>...</LogEvent>\"}'"

#!/usr/bin/env python3
"""
Quick test script to validate AgentBusiedOut event processing.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent_event_processor.utils.xml_parser import AgentEventXMLParser
from agent_event_processor.models.events import AgentEvent
from agent_event_processor.utils.field_mapping import FieldMappingConfig

def test_xml_parsing():
    """Test XML parsing of AgentBusiedOut event."""
    xml_content = '''<LogEvent xmlns="http://solacom.com/Logging">
<timestamp>2025-02-05T14:34:08.951Z</timestamp>
<agencyOrElement>Brandon911</agencyOrElement>
<agent>keem</agent>
<eventType>AgentBusiedOut</eventType>
<agentbusiedout>
<mediaLabel>_ML_194D5F04D38C0001C46D@BrandonMB</mediaLabel>
<uri>tel:+2045553007</uri>
<agentRole>BPS - CT_Dispatch</agentRole>
<workstation>OP7</workstation>
<reason>break</reason>
<action>manual</action>
<duration>900</duration>
</agentbusiedout>
</LogEvent>'''

    print("Testing XML parsing...")
    result = AgentEventXMLParser.xml_to_json(xml_content)
    print(f"✓ XML parsed successfully!")
    print(f"  Event type: {result['eventType']}")
    print(f"  Agent: {result['agent']}")
    print(f"  Reason: {result['agentbusiedout']['reason']}")
    print(f"  Action: {result['agentbusiedout']['action']}")
    print(f"  Duration: {result['agentbusiedout']['duration']}")
    return result

def test_event_model(json_data):
    """Test AgentEvent model validation."""
    print("\nTesting event model...")
    
    # Prepare hierarchical data
    core_fields = {"timestamp", "eventType", "agencyOrElement", "agent"}
    event_fields = {}
    event_data = {}
    
    for key, value in json_data.items():
        if key.startswith("@"):
            continue
        if key in core_fields:
            event_fields[key] = value
        else:
            event_data[key] = value
    
    result = event_fields.copy()
    result["event_data"] = event_data
    
    event = AgentEvent.model_validate(result)
    print(f"✓ Event model created successfully!")
    print(f"  Event type: {event.event_type}")
    print(f"  Agent: {event.agent}")
    print(f"  Reason: {event.get_value('reason')}")
    print(f"  Action: {event.get_value('action')}")
    print(f"  Duration: {event.get_value('duration')}")
    return event

def test_field_mapping(event):
    """Test field mapping extraction."""
    print("\nTesting field mapping...")
    
    extracted = FieldMappingConfig.extract_fields(event, event.event_type)
    print(f"✓ Field mapping extracted successfully!")
    print(f"  Extracted fields: {extracted}")
    
    expected_fields = ['reason_code', 'busied_out_action', 'busied_out_duration', 'media_label', 'workstation']
    for field in expected_fields:
        if field in extracted:
            print(f"  ✓ {field}: {extracted[field]}")
        else:
            print(f"  ✗ {field}: MISSING")
    
    return extracted

if __name__ == "__main__":
    try:
        # Test XML parsing
        json_data = test_xml_parsing()
        
        # Test event model
        event = test_event_model(json_data)
        
        # Test field mapping
        extracted = test_field_mapping(event)
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

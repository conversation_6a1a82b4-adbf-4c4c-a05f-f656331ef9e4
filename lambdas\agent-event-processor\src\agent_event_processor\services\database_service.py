"""
Database service for Agent Event Processor.

This module provides database connectivity using:
- Redshift Data API for AWS environments (dev/qa/prod)
- psycopg2 for local development with PostgreSQL

Features:
- Automatic environment detection
- Retry logic with exponential backoff
- Proper error handling and logging
- Connection pooling for psycopg2
- Query timeouts and statement groups
- Duplicate key handling
"""

import os
import time
import uuid
import json
from contextlib import contextmanager
from typing import Any, Dict, Generator, Optional, List, Union, Tuple
from enum import Enum
from datetime import datetime
from decimal import Decimal

import boto3
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.pool import ThreadedConnectionPool
from botocore.exceptions import ClientError, BotoCoreError
from aws_lambda_powertools import Logger
import pytz
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)

from ..config.settings import DatabaseSettings
from ..utils.database_utils import get_db_credentials

logger = Logger()


class DatabaseEnvironment(Enum):
    """Database environment types."""

    LOCAL = "local"
    AWS = "aws"


class DatabaseError(Exception):
    """Base exception for database operations."""

    pass


class DuplicateKeyError(DatabaseError):
    """Exception for duplicate key constraint violations."""

    pass


class DatabaseService:
    """
    Database service with environment-aware connectivity.

    Automatically detects environment and uses appropriate database connection:
    - Local: PostgreSQL with psycopg2
    - AWS: Redshift with Data API
    """

    def __init__(self, settings: DatabaseSettings):
        """
        Initialize database service.

        Args:
            settings: Database configuration settings.
        """
        self.settings = settings
        self.environment = self._detect_environment()

        # Initialize based on environment
        if self.environment == DatabaseEnvironment.LOCAL:
            self._init_local_connection()
        else:
            self._init_redshift_data_api()

        logger.info(
            "Database service initialized",
            environment=self.environment.value,
            connection_type=(
                "psycopg2"
                if self.environment == DatabaseEnvironment.LOCAL
                else "redshift-data-api"
            ),
        )

    def _detect_environment(self) -> DatabaseEnvironment:
        """Detect current environment based on configuration."""
        # Check if we have direct database connection settings (local)
        if (
            self.settings.host
            and self.settings.port
            and self.settings.name
            and self.settings.user
            and self.settings.password
        ):
            return DatabaseEnvironment.LOCAL

        # Check environment variable
        env = os.getenv("ENVIRONMENT", "").lower()
        if env in ["local", "development"]:
            return DatabaseEnvironment.LOCAL

        return DatabaseEnvironment.AWS

    def _init_local_connection(self):
        """Initialize PostgreSQL connection for local development."""
        self._pool = None
        self._connection_params = {
            "host": self.settings.host,
            "port": self.settings.port,
            "database": self.settings.name,
            "user": self.settings.user,
            "password": self.settings.password,
            "connect_timeout": self.settings.connection_timeout,
            "application_name": "agent-event-processor",
        }

        # Create connection pool
        try:
            self._pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=min(self.settings.max_connections, 5),
                **self._connection_params,
            )
            logger.info("PostgreSQL connection pool initialized")
        except Exception as e:
            logger.error(
                "Failed to initialize PostgreSQL connection pool", error=str(e)
            )
            raise

    def _init_redshift_data_api(self):
        """Initialize Redshift Data API client."""
        try:
            self.redshift_client = boto3.client("redshift-data")

            # Get cluster configuration from secrets or environment
            if self.settings.secret_name:
                credentials = get_db_credentials(self.settings.secret_name)
                self.cluster_identifier = getattr(
                    credentials,
                    "cluster_identifier",
                    os.getenv("REDSHIFT_CLUSTER_ID", "default-cluster"),
                )
                self.database_name = credentials.database
                self.db_user = credentials.username
            else:
                self.cluster_identifier = os.getenv(
                    "REDSHIFT_CLUSTER_ID", "default-cluster"
                )
                self.database_name = os.getenv("REDSHIFT_DATABASE", "analytics")
                self.db_user = os.getenv("REDSHIFT_USER", "analytics_user")

            # Query group for monitoring and resource management
            self.query_group = os.getenv(
                "REDSHIFT_QUERY_GROUP", "agent-event-processor"
            )

            logger.info(
                "Redshift Data API initialized",
                cluster_identifier=self.cluster_identifier,
                database=self.database_name,
                query_group=self.query_group,
            )
        except Exception as e:
            logger.error("Failed to initialize Redshift Data API", error=str(e))
            raise

    @contextmanager
    def get_connection(
        self,
    ) -> Generator[
        Union[psycopg2.extensions.connection, "RedshiftDataAPIConnection"], None, None
    ]:
        """
        Get database connection based on environment.

        Returns:
            Database connection (psycopg2 for local, Data API wrapper for AWS)
        """
        if self.environment == DatabaseEnvironment.LOCAL:
            with self._get_local_connection() as conn:
                yield conn
        else:
            yield self._get_redshift_connection()

    @contextmanager
    def _get_local_connection(
        self,
    ) -> Generator[psycopg2.extensions.connection, None, None]:
        """Get PostgreSQL connection from pool."""
        conn = None
        try:
            conn = self._pool.getconn()
            if conn:
                # Test connection
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute("SELECT 1")

            yield conn

        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except Exception as rollback_error:
                    logger.error(
                        "Failed to rollback transaction", error=str(rollback_error)
                    )
            raise
        finally:
            if conn:
                try:
                    self._pool.putconn(conn)
                except Exception as e:
                    logger.error("Failed to return connection to pool", error=str(e))

    def _get_redshift_connection(self) -> "RedshiftDataAPIConnection":
        """Get Redshift Data API connection wrapper."""
        return RedshiftDataAPIConnection(
            client=self.redshift_client,
            cluster_identifier=self.cluster_identifier,
            database=self.database_name,
            db_user=self.db_user,
            query_timeout=self.settings.query_timeout,
            query_group=self.query_group,
        )


class RedshiftDataAPIConnection:
    """
    Wrapper for Redshift Data API to provide psycopg2-like interface.
    """

    def __init__(
        self,
        client,
        cluster_identifier: str,
        database: str,
        db_user: str,
        query_timeout: int = 300,
        query_group: str = None,
    ):
        self.client = client
        self.cluster_identifier = cluster_identifier
        self.database = database
        self.db_user = db_user
        self.query_timeout = query_timeout
        self.query_group = query_group or "agent-event-processor"
        self._in_transaction = False

    def cursor(self):
        """Return a cursor-like object for executing queries."""
        return RedshiftDataAPICursor(self)

    def commit(self):
        """Commit transaction (no-op for Data API as it auto-commits)."""
        self._in_transaction = False

    def rollback(self):
        """Rollback transaction (limited support in Data API)."""
        self._in_transaction = False
        logger.warning("Rollback called on Redshift Data API - limited support")

    def close(self):
        """Close connection (no-op for Data API)."""
        pass

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.rollback()
        else:
            self.commit()


class RedshiftDataAPICursor:
    """
    Cursor-like interface for Redshift Data API.
    """

    def __init__(self, connection: RedshiftDataAPIConnection):
        self.connection = connection
        self._results = None
        self._column_metadata = None

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ClientError, BotoCoreError)),
        before_sleep=before_sleep_log(logger, "WARNING"),
    )
    def execute(self, query: str, params: Optional[List] = None):
        """
        Execute SQL query with proper parameterized values.

        Args:
            query: SQL query with %s placeholders for PostgreSQL or named parameters for Redshift
            params: Query parameters (properly typed)
        """
        try:
            # Convert psycopg2-style placeholders to Redshift Data API parameters
            redshift_query, redshift_params = self._convert_query_and_params(
                query, params
            )

            # Build execute statement request
            request_params = {
                "ClusterIdentifier": self.connection.cluster_identifier,
                "Database": self.connection.database,
                "DbUser": self.connection.db_user,
                "Sql": redshift_query,
                "StatementName": f"agent-event-processor-{uuid.uuid4().hex[:8]}",
                "WithEvent": True,
            }

            # Add parameters if present
            if redshift_params:
                request_params["Parameters"] = redshift_params

            # Add query group for resource management
            if self.connection.query_group:
                request_params["Sql"] = (
                    f"SET query_group TO '{self.connection.query_group}'; {redshift_query}"
                )

            # Execute query
            response = self.connection.client.execute_statement(**request_params)

            statement_id = response["Id"]

            # Wait for completion
            self._wait_for_completion(statement_id)

            # Get results if it's a SELECT query
            if redshift_query.strip().upper().startswith("SELECT"):
                self._fetch_results(statement_id)

            logger.debug(
                "Query executed successfully",
                statement_id=statement_id,
                query_preview=redshift_query[:100],
                param_count=len(redshift_params) if redshift_params else 0,
            )

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))

            # Handle duplicate key errors gracefully
            if (
                "duplicate key" in error_message.lower()
                or "unique constraint" in error_message.lower()
            ):
                logger.warning(
                    "Duplicate key constraint violation - continuing",
                    error_message=error_message,
                    query_preview=query[:100] if len(query) > 100 else query,
                )
                raise DuplicateKeyError(
                    f"Duplicate key violation: {error_message}"
                ) from e

            logger.error(
                "Redshift Data API query failed",
                error_code=error_code,
                error_message=error_message,
                query_preview=query[:100] if len(query) > 100 else query,
            )
            raise

        except Exception as e:
            logger.error(
                "Unexpected error executing query",
                error=str(e),
                query_preview=query[:100] if len(query) > 100 else query,
            )
            raise

    def _convert_query_and_params(
        self, query: str, params: Optional[List]
    ) -> Tuple[str, Optional[List[Dict]]]:
        """
        Convert psycopg2-style query and parameters to Redshift Data API format.

        Args:
            query: SQL query with %s placeholders
            params: List of parameter values

        Returns:
            Tuple of (converted_query, redshift_parameters)
        """
        if not params:
            return query, None

        # Convert %s placeholders to :param1, :param2, etc.
        converted_query = query
        redshift_params = []

        for i, param in enumerate(params):
            param_name = f"param{i + 1}"
            converted_query = converted_query.replace("%s", f":{param_name}", 1)

            # Convert parameter to Redshift Data API format
            redshift_param = self._convert_parameter(param_name, param)
            redshift_params.append(redshift_param)

        return converted_query, redshift_params

    def _convert_parameter(self, name: str, value: Any) -> Dict[str, Any]:
        """
        Convert Python value to Redshift Data API parameter format.

        Args:
            name: Parameter name
            value: Parameter value

        Returns:
            Redshift Data API parameter dictionary
        """
        param = {"name": name}

        if value is None:
            param["value"] = {"isNull": True}
        elif isinstance(value, bool):
            param["value"] = {"booleanValue": value}
        elif isinstance(value, int):
            param["value"] = {"longValue": value}
        elif isinstance(value, float):
            param["value"] = {"doubleValue": value}
        elif isinstance(value, Decimal):
            param["value"] = {"stringValue": str(value)}
        elif isinstance(value, datetime):
            param["value"] = {"stringValue": value.isoformat()}
        elif isinstance(value, str):
            param["value"] = {"stringValue": value}
        else:
            # Convert other types to string
            param["value"] = {"stringValue": str(value)}

        return param

    def _wait_for_completion(self, statement_id: str):
        """Wait for query completion with timeout."""
        start_time = time.time()

        while time.time() - start_time < self.connection.query_timeout:
            try:
                response = self.connection.client.describe_statement(Id=statement_id)
                status = response["Status"]

                if status == "FINISHED":
                    return
                elif status in ["FAILED", "ABORTED"]:
                    error_message = response.get("Error", "Query failed")
                    raise DatabaseError(f"Query failed: {error_message}")

                # Wait before checking again
                time.sleep(1)

            except ClientError as e:
                logger.error("Error checking query status", error=str(e))
                raise

        raise DatabaseError(
            f"Query timeout after {self.connection.query_timeout} seconds"
        )

    def _fetch_results(self, statement_id: str):
        """Fetch query results."""
        try:
            response = self.connection.client.get_statement_result(Id=statement_id)
            self._results = response.get("Records", [])
        except ClientError as e:
            logger.error("Error fetching query results", error=str(e))
            raise

    def fetchone(self) -> Optional[Dict[str, Any]]:
        """Fetch one result row."""
        if self._results and len(self._results) > 0:
            return self._convert_result_row(self._results.pop(0))
        return None

    def fetchall(self) -> List[Dict[str, Any]]:
        """Fetch all result rows."""
        if not self._results:
            return []

        results = [self._convert_result_row(row) for row in self._results]
        self._results = []
        return results

    def _convert_result_row(self, row: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Convert Redshift Data API result row to dictionary."""
        # This is a simplified conversion - in production, you'd want to handle
        # column names and types properly
        result = {}
        for i, field in enumerate(row):
            # Extract value based on type
            if "stringValue" in field:
                value = field["stringValue"]
            elif "longValue" in field:
                value = field["longValue"]
            elif "doubleValue" in field:
                value = field["doubleValue"]
            elif "booleanValue" in field:
                value = field["booleanValue"]
            elif "isNull" in field and field["isNull"]:
                value = None
            else:
                value = str(field)

            result[f"column_{i}"] = value

        return result

    def close(self):
        """Close cursor."""
        self._results = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class DimensionTableManager:
    """
    Manages dimension table operations with SCD Type 2 support.

    Handles tenant, agent, and queue dimensions with proper versioning
    and change tracking for data warehouse operations.
    """

    def __init__(self, database_service: DatabaseService):
        """
        Initialize dimension table manager.

        Args:
            database_service: Database service instance.
        """
        self.db_service = database_service
        logger.info("Dimension table manager initialized")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type(DatabaseError),
        before_sleep=before_sleep_log(logger, "WARNING"),
    )
    def get_or_create_tenant_key(
        self, tenant_name: str, timezone_name: str = "UTC"
    ) -> int:
        """
        Get or create tenant dimension key.

        Args:
            tenant_name: Name of the tenant.
            timezone_name: Tenant timezone.

        Returns:
            int: Tenant dimension key.
        """
        try:
            with self.db_service.get_connection() as conn:
                cursor_factory = (
                    RealDictCursor
                    if self.db_service.environment == DatabaseEnvironment.LOCAL
                    else None
                )
                with conn.cursor(cursor_factory=cursor_factory) as cursor:
                    # Check if tenant exists
                    cursor.execute(
                        "SELECT tenant_key FROM dim_tenant WHERE tenant_name = %s",
                        [tenant_name],
                    )

                    result = cursor.fetchone()
                    if result:
                        tenant_key = result.get("tenant_key") or result.get("column_0")
                        logger.debug(
                            "Found existing tenant",
                            tenant_name=tenant_name,
                            tenant_key=tenant_key,
                        )
                        return tenant_key

                    # Create new tenant
                    cursor.execute(
                        """
                        INSERT INTO dim_tenant (tenant_name, timezone_name)
                        VALUES (%s, %s)
                        """,
                        [tenant_name, timezone_name],
                    )

                    # Get the new tenant key
                    if self.db_service.environment == DatabaseEnvironment.LOCAL:
                        cursor.execute("SELECT lastval()")
                        result = cursor.fetchone()
                        tenant_key = result.get("lastval") or result.get("column_0")
                    else:
                        # For Redshift, we need to query back
                        cursor.execute(
                            "SELECT tenant_key FROM dim_tenant WHERE tenant_name = %s",
                            [tenant_name],
                        )
                        result = cursor.fetchone()
                        tenant_key = result.get("tenant_key") or result.get("column_0")

                    conn.commit()

                    logger.info(
                        "Created new tenant",
                        tenant_name=tenant_name,
                        tenant_key=tenant_key,
                        timezone_name=timezone_name,
                    )

                    return tenant_key

        except DuplicateKeyError:
            # Handle race condition - another process created the tenant
            logger.info(
                "Tenant created by another process, fetching existing",
                tenant_name=tenant_name,
            )
            return self.get_or_create_tenant_key(tenant_name, timezone_name)

        except Exception as e:
            logger.error(
                "Failed to get or create tenant",
                tenant_name=tenant_name,
                error=str(e),
                exc_info=True,
            )
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type(DatabaseError),
        before_sleep=before_sleep_log(logger, "WARNING"),
    )
    def upsert_agent_dimension(
        self, agent_data: Dict[str, Any], tenant_key: int
    ) -> int:
        """
        Upsert agent dimension with SCD Type 2 support.

        Args:
            agent_data: Agent information dictionary.
            tenant_key: Tenant dimension key.

        Returns:
            int: Agent dimension key.
        """
        try:
            with self.db_service.get_connection() as conn:
                cursor_factory = (
                    RealDictCursor
                    if self.db_service.environment == DatabaseEnvironment.LOCAL
                    else None
                )
                with conn.cursor(cursor_factory=cursor_factory) as cursor:
                    agent_name = agent_data.get("agent_name")

                    # Check for current active record
                    cursor.execute(
                        """
                        SELECT agent_key, operator_id, agent_role, agent_uri, workstation
                        FROM dim_agent
                        WHERE tenant_key = %s AND agent_name = %s AND is_current = TRUE
                        """,
                        [tenant_key, agent_name],
                    )

                    current_record = cursor.fetchone()

                    if current_record:
                        # Check if attributes have changed
                        if self._agent_attributes_changed(current_record, agent_data):
                            # Close current record
                            cursor.execute(
                                """
                                UPDATE dim_agent
                                SET is_current = FALSE, valid_to = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                                WHERE tenant_key = %s AND agent_name = %s AND is_current = TRUE
                                """,
                                [tenant_key, agent_name],
                            )

                            # Insert new record
                            return self._insert_new_agent_record(
                                cursor, agent_data, tenant_key, conn
                            )
                        else:
                            # No changes, return existing key
                            agent_key = current_record.get(
                                "agent_key"
                            ) or current_record.get("column_0")
                            logger.debug(
                                "Agent unchanged, returning existing key",
                                agent_name=agent_name,
                                agent_key=agent_key,
                            )
                            return agent_key
                    else:
                        # New agent, insert record
                        return self._insert_new_agent_record(
                            cursor, agent_data, tenant_key, conn
                        )

        except DuplicateKeyError:
            # Handle race condition
            logger.info(
                "Agent created by another process, fetching existing",
                agent_name=agent_data.get("agent_name"),
            )
            return self.upsert_agent_dimension(agent_data, tenant_key)

        except Exception as e:
            logger.error(
                "Failed to upsert agent dimension",
                agent_data=agent_data,
                tenant_key=tenant_key,
                error=str(e),
                exc_info=True,
            )
            raise

    def _agent_attributes_changed(
        self, current: Dict[str, Any], new_data: Dict[str, Any]
    ) -> bool:
        """Check if agent attributes have changed."""
        fields_to_check = ["operator_id", "agent_role", "agent_uri", "workstation"]

        for field in fields_to_check:
            current_value = current.get(field)
            new_value = new_data.get(field)

            if current_value != new_value:
                logger.debug(
                    "Agent attribute changed",
                    field=field,
                    old_value=current_value,
                    new_value=new_value,
                )
                return True

        return False

    def _insert_new_agent_record(
        self, cursor, agent_data: Dict[str, Any], tenant_key: int, conn
    ) -> int:
        """Insert new agent record."""
        cursor.execute(
            """
            INSERT INTO dim_agent (
                agent_name, operator_id, agent_role, agent_uri, workstation, tenant_key,
                valid_from, is_current
            ) VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, TRUE)
            """,
            [
                agent_data.get("agent_name"),
                agent_data.get("operator_id"),
                agent_data.get("agent_role"),
                agent_data.get("agent_uri"),
                agent_data.get("workstation"),
                tenant_key,
            ],
        )

        # Get the new agent key
        if self.db_service.environment == DatabaseEnvironment.LOCAL:
            cursor.execute("SELECT lastval()")
            result = cursor.fetchone()
            agent_key = result.get("lastval") or result.get("column_0")
        else:
            # For Redshift, query back
            cursor.execute(
                """
                SELECT agent_key FROM dim_agent
                WHERE tenant_key = %s AND agent_name = %s AND is_current = TRUE
                """,
                [tenant_key, agent_data.get("agent_name")],
            )
            result = cursor.fetchone()
            agent_key = result.get("agent_key") or result.get("column_0")

        conn.commit()

        logger.info(
            "Created new agent record",
            agent_name=agent_data.get("agent_name"),
            agent_key=agent_key,
            tenant_key=tenant_key,
        )

        return agent_key

    def get_date_key(self, date_value: datetime) -> int:
        """
        Get date dimension key in YYYYMMDD format.

        Args:
            date_value: Date to convert.

        Returns:
            int: Date key in YYYYMMDD format.
        """
        return int(date_value.strftime("%Y%m%d"))

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type((psycopg2.OperationalError, ClientError)),
        before_sleep=before_sleep_log(logger, "WARNING"),
    )
    def get_or_create_queue_key(
        self, queue_data: Dict[str, Any], tenant_key: int
    ) -> Optional[int]:
        """
        Get or create queue dimension key.

        Args:
            queue_data: Queue information containing ring_group_name and ring_group_uri
            tenant_key: Tenant key for the queue

        Returns:
            Queue key if queue data is provided, None otherwise
        """
        if not queue_data.get("ring_group_name"):
            return None

        try:
            with self.db_service.get_connection() as conn:
                cursor_factory = (
                    RealDictCursor
                    if self.db_service.environment == DatabaseEnvironment.LOCAL
                    else None
                )
                with conn.cursor(cursor_factory=cursor_factory) as cursor:

                    # Check if queue already exists
                    select_sql = """
                        SELECT queue_key
                        FROM dim_queue
                        WHERE ring_group_name = %s
                        AND tenant_key = %s
                        AND is_current = TRUE
                    """

                    cursor.execute(
                        select_sql, [queue_data["ring_group_name"], tenant_key]
                    )

                    result = cursor.fetchone()
                    if result:
                        queue_key = result.get("queue_key") or result.get("column_0")
                        logger.debug(
                            "Queue dimension found",
                            queue_key=queue_key,
                            ring_group_name=queue_data["ring_group_name"],
                        )
                        return queue_key

                    # Create new queue dimension
                    insert_sql = """
                        INSERT INTO dim_queue (
                            ring_group_name, ring_group_uri, tenant_key,
                            valid_from, is_current, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """

                    now = datetime.now(tz=pytz.UTC)
                    cursor.execute(
                        insert_sql,
                        [
                            queue_data["ring_group_name"],
                            queue_data.get("ring_group_uri"),
                            tenant_key,
                            now,
                            True,
                            now,
                            now,
                        ],
                    )

                    # Get the new queue key
                    if self.db_service.environment == DatabaseEnvironment.LOCAL:
                        cursor.execute("SELECT lastval()")
                        result = cursor.fetchone()
                        queue_key = result.get("lastval") or result.get("column_0")
                    else:
                        # For Redshift, query back
                        cursor.execute(
                            "SELECT queue_key FROM dim_queue WHERE ring_group_name = %s AND tenant_key = %s AND is_current = TRUE",
                            [queue_data["ring_group_name"], tenant_key],
                        )
                        result = cursor.fetchone()
                        queue_key = result.get("queue_key") or result.get("column_0")

                    conn.commit()

                    logger.info(
                        "Queue dimension created",
                        queue_key=queue_key,
                        ring_group_name=queue_data["ring_group_name"],
                        tenant_key=tenant_key,
                    )

                    return queue_key

        except Exception as e:
            logger.error(
                "Failed to get or create queue dimension",
                error=str(e),
                queue_data=queue_data,
                tenant_key=tenant_key,
                exc_info=True,
            )
            raise


class FactTableManager:
    """
    Manages fact table operations for agent events.

    Handles insertion of fact records with proper error handling
    and duplicate key management.
    """

    def __init__(self, database_service: DatabaseService):
        """
        Initialize fact table manager.

        Args:
            database_service: Database service instance.
        """
        self.db_service = database_service
        logger.info("Fact table manager initialized")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type(DatabaseError),
        before_sleep=before_sleep_log(logger, "WARNING"),
    )
    def insert_agent_event(self, fact_data: Dict[str, Any]) -> bool:
        """
        Insert agent event into fact table.

        Args:
            fact_data: Fact table data to insert.

        Returns:
            bool: True if inserted successfully, False if duplicate.
        """
        try:
            with self.db_service.get_connection() as conn:
                cursor_factory = (
                    RealDictCursor
                    if self.db_service.environment == DatabaseEnvironment.LOCAL
                    else None
                )
                with conn.cursor(cursor_factory=cursor_factory) as cursor:
                    # Convert JSON data to string for JSONB field
                    processed_data = fact_data.copy()
                    if (
                        "event_data_json" in processed_data
                        and processed_data["event_data_json"] is not None
                    ):
                        processed_data["event_data_json"] = json.dumps(
                            processed_data["event_data_json"]
                        )

                    # Build INSERT statement
                    columns = list(processed_data.keys())
                    placeholders = ["%s"] * len(columns)
                    values = list(processed_data.values())

                    insert_sql = f"""
                        INSERT INTO fact_agent_event ({', '.join(columns)})
                        VALUES ({', '.join(placeholders)})
                        ON CONFLICT (agent_key, tenant_key, event_timestamp_utc, event_type, media_label)
                        DO NOTHING
                        RETURNING state_key
                    """

                    cursor.execute(insert_sql, values)
                    result = cursor.fetchone()
                    conn.commit()

                    if result:
                        logger.debug(
                            "Agent event inserted successfully",
                            table="fact_agent_event",
                            columns=len(columns),
                            event_type=fact_data.get("event_type"),
                            agent_key=fact_data.get("agent_key"),
                            state_key=(
                                result[0]
                                if isinstance(result, (list, tuple))
                                else result.get("state_key")
                            ),
                        )
                        return True
                    else:
                        logger.info(
                            "Duplicate agent event detected and skipped",
                            table="fact_agent_event",
                            event_type=fact_data.get("event_type"),
                            agent_key=fact_data.get("agent_key"),
                            timestamp=fact_data.get("event_timestamp_utc"),
                        )
                        return False

        except DuplicateKeyError as e:
            logger.warning(
                "Duplicate agent event - skipping",
                error=str(e),
                event_type=fact_data.get("event_type"),
                agent_key=fact_data.get("agent_key"),
                event_timestamp=fact_data.get("event_timestamp_utc"),
            )
            return False

        except Exception as e:
            logger.error(
                "Failed to insert agent event",
                error=str(e),
                fact_data=fact_data,
                exc_info=True,
            )
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type(DatabaseError),
        before_sleep=before_sleep_log(logger, "WARNING"),
    )
    def upsert_agent_interval(self, interval_data: Dict[str, Any]) -> bool:
        """
        Upsert agent interval record.

        Args:
            interval_data: Interval data to upsert.

        Returns:
            bool: True if operation successful.
        """
        try:
            with self.db_service.get_connection() as conn:
                cursor_factory = (
                    RealDictCursor
                    if self.db_service.environment == DatabaseEnvironment.LOCAL
                    else None
                )
                with conn.cursor(cursor_factory=cursor_factory) as cursor:
                    agent_key = interval_data["agent_key"]
                    interval_type = interval_data["interval_type"]

                    # Check for existing open interval
                    cursor.execute(
                        """
                        SELECT interval_key FROM fact_agent_intervals
                        WHERE agent_key = %s AND interval_type = %s AND is_current_interval = TRUE
                        """,
                        [agent_key, interval_type],
                    )

                    existing = cursor.fetchone()

                    if existing and interval_data.get("interval_end_utc"):
                        # Close existing interval
                        interval_key = existing.get("interval_key") or existing.get(
                            "column_0"
                        )
                        cursor.execute(
                            """
                            UPDATE fact_agent_intervals
                            SET interval_end_utc = %s,
                                interval_end_local = %s,
                                duration_seconds = EXTRACT(EPOCH FROM (%s - interval_start_utc)),
                                is_current_interval = FALSE
                            WHERE interval_key = %s
                            """,
                            [
                                interval_data["interval_end_utc"],
                                interval_data.get("interval_end_local"),
                                interval_data["interval_end_utc"],
                                interval_key,
                            ],
                        )

                        logger.debug(
                            "Closed agent interval",
                            interval_key=interval_key,
                            agent_key=agent_key,
                            interval_type=interval_type,
                        )

                    elif not existing:
                        # Insert new interval
                        columns = list(interval_data.keys())
                        placeholders = ["%s"] * len(columns)
                        values = list(interval_data.values())

                        insert_sql = f"""
                            INSERT INTO fact_agent_intervals ({', '.join(columns)})
                            VALUES ({', '.join(placeholders)})
                        """

                        cursor.execute(insert_sql, values)

                        logger.debug(
                            "Created new agent interval",
                            agent_key=agent_key,
                            interval_type=interval_type,
                            is_current=interval_data.get("is_current_interval", False),
                        )

                    conn.commit()
                    return True

        except DuplicateKeyError as e:
            logger.warning(
                "Duplicate agent interval - skipping",
                error=str(e),
                agent_key=interval_data.get("agent_key"),
                interval_type=interval_data.get("interval_type"),
            )
            return False

        except Exception as e:
            logger.error(
                "Failed to upsert agent interval",
                error=str(e),
                interval_data=interval_data,
                exc_info=True,
            )
            raise

    def health_check(self) -> bool:
        """
        Perform health check on database connection.

        Returns:
            bool: True if healthy, False otherwise.
        """
        try:
            with self.db_service.get_connection() as conn:
                cursor_factory = (
                    RealDictCursor
                    if self.db_service.environment == DatabaseEnvironment.LOCAL
                    else None
                )
                with conn.cursor(cursor_factory=cursor_factory) as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()

                    is_healthy = result is not None
                    logger.debug(
                        "Database health check completed", is_healthy=is_healthy
                    )
                    return is_healthy

        except Exception as e:
            logger.warning("Database health check failed", error=str(e))
            return False

["::TestEventProcessor::test_transform_logout_event_with_voice_qos", "::TestEventProcessor::test_transform_queue_state_change_event", "test_validation.py::test_database_schema", "test_validation.py::test_field_mapping", "test_validation.py::test_xml_parsing", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_batch_event_processing", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_busied_out_event_processing", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_database_connectivity", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_invalid_xml_handling", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_response_format", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_timeout_handling", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_login_event_processing", "tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_missing_required_fields", "tests/unit/test_event_processor.py::TestEventProcessor::test_insert_fact_record_duplicate", "tests/unit/test_event_processor.py::TestEventProcessor::test_insert_fact_record_success", "tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_failure", "tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_success", "tests/unit/test_event_processor.py::TestEventProcessor::test_resolve_dimension_keys", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_to_fact_data", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_acd_login_event_full_invocation", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_all_event_types_batch", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_empty_records", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_login_event_full_invocation", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_metrics_configuration", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_partial_batch_failure", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_sns_wrapped_message", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_acd_login_event_processing", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_batch_processing_multiple_events", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_empty_records", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_invalid_xml_handling", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_partial_batch_failure", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_sns_wrapped_message", "tests/unit/test_lambda_function.py::TestLambdaHandler::test_successful_login_event_processing", "tests/unit/test_models.py::TestAgentEvent::test_acd_event_validation", "tests/unit/test_models.py::TestAgentEvent::test_extra_fields_rejected", "tests/unit/test_models.py::TestAgentEvent::test_invalid_agent_uri", "tests/unit/test_models.py::TestAgentEvent::test_missing_required_fields", "tests/unit/test_models.py::TestAgentEvent::test_timestamp_parsing", "tests/unit/test_models.py::TestAgentEvent::test_valid_acd_login_event", "tests/unit/test_models.py::TestAgentEvent::test_valid_login_event", "tests/unit/test_models.py::TestDatabaseCredentials::test_empty_host", "tests/unit/test_models.py::TestDatabaseCredentials::test_invalid_port", "tests/unit/test_models.py::TestDatabaseCredentials::test_valid_credentials", "tests/unit/test_models.py::TestDimensionKeys::test_invalid_date_key", "tests/unit/test_models.py::TestDimensionKeys::test_invalid_time_key", "tests/unit/test_models.py::TestDimensionKeys::test_valid_dimension_keys"]